@props([
    'selected' => 'ri-check-line',
    'name' => 'icon_class',
    'required' => true,
    'label' => 'Select Icon',
    'id' => null
])

@php
    // Generate unique ID if not provided
    $componentId = $id ?? 'icon-selector-' . uniqid();
    $inputId = $name;
    $displayId = $componentId . '-display';
    $tabsId = $componentId . '-tabs';
    $remixTabId = $componentId . '-remix-tab';
    $boxiconsTabId = $componentId . '-boxicons-tab';
    $remixContentId = $componentId . '-remix-icons';
    $boxiconsContentId = $componentId . '-boxicons-icons';

    // Get Remix Icons
    $remixCssPath = public_path('assets/icon-fonts/RemixIcons/fonts/remixicon.css');
    $remixIcons = [];
    if (file_exists($remixCssPath)) {
        $css = file_get_contents($remixCssPath);
        preg_match_all('/\.((ri-[a-z0-9\-]+-line))\:before/', $css, $matches);
        $remixIcons = array_unique($matches[1]);
        sort($remixIcons);
    }

    // Get Boxicons
    $boxiconsCssPath = public_path('assets/icon-fonts/boxicons/css/boxicons.css');
    $boxicons = [];
    if (file_exists($boxiconsCssPath)) {
        $css = file_get_contents($boxiconsCssPath);
        preg_match_all('/\.((bx[s]?-[a-z0-9\-]+))\:before/', $css, $matches);
        $boxicons = array_unique($matches[1]);
        sort($boxicons);
    }

    // Determine which icon set is currently selected and extract the specific icon class
    $selectedIconSet = 'remix';
    $selectedSpecificIcon = $selected;

    if (strpos($selected, 'bx') === 0) {
        $selectedIconSet = 'boxicons';
        // Extract the specific icon class from complete Boxicon class (e.g., 'bx bx-table' -> 'bx-table')
        if (strpos($selected, 'bx ') === 0) {
            $selectedSpecificIcon = substr($selected, 3); // Remove 'bx ' prefix
        }
    }
@endphp

<div class="mb-3" id="{{ $componentId }}">
    <label class="form-label" for="{{ $inputId }}">
        {{ $label }}
        @if($required)
            <span class="text-danger">*</span>
        @endif
    </label>

    <!-- Unified Icon Picker Container -->
    <div class="icon-picker-container border rounded">
        <!-- Icon Set Button Group Header -->
        <div class="icon-picker-header d-flex justify-content-left p-3 border-bottom bg-light rounded-top">
            <div class="btn-group" id="{{ $tabsId }}" role="group" aria-label="Icon set selection">
                <input type="radio" class="btn-check" name="{{ $componentId }}-icon-set" id="{{ $remixTabId }}"
                       autocomplete="off" {{ $selectedIconSet === 'remix' ? 'checked' : '' }}
                       data-bs-toggle="tab" data-bs-target="#{{ $remixContentId }}"
                       aria-controls="{{ $remixContentId }}" aria-selected="{{ $selectedIconSet === 'remix' ? 'true' : 'false' }}">
                <label class="btn btn-outline-secondary" for="{{ $remixTabId }}">
                    <i class="ri-remixicon-line me-2"></i>Remix Icons ({{ count($remixIcons) }})
                </label>

                <input type="radio" class="btn-check" name="{{ $componentId }}-icon-set" id="{{ $boxiconsTabId }}"
                       autocomplete="off" {{ $selectedIconSet === 'boxicons' ? 'checked' : '' }}
                       data-bs-toggle="tab" data-bs-target="#{{ $boxiconsContentId }}"
                       aria-controls="{{ $boxiconsContentId }}" aria-selected="{{ $selectedIconSet === 'boxicons' ? 'true' : 'false' }}">
                <label class="btn btn-outline-secondary" for="{{ $boxiconsTabId }}">
                    <i class="bx bx-box me-2"></i>Boxicons ({{ count($boxicons) }})
                </label>
            </div>
        </div>

        <!-- Icon Set Content -->
        <div class="tab-content" id="{{ $componentId }}-tab-content">
            <!-- Remix Icons Tab -->
            <div class="tab-pane fade {{ $selectedIconSet === 'remix' ? 'show active' : '' }}"
                 id="{{ $remixContentId }}" role="tabpanel" aria-labelledby="{{ $remixTabId }}">
                <div class="d-flex flex-wrap gap-3 icon-picker"
                     style="max-height: 300px; overflow-y: auto; padding: 8px;" data-icon-set="remix">
                    @if(!empty($remixIcons))
                        @foreach ($remixIcons as $icon)
                            <div class="icon-option border rounded p-2 {{ $selected === $icon ? 'border-primary' : '' }}"
                                style="cursor: pointer; min-width: 50px; text-align: center;"
                                data-icon="{{ $icon }}" title="{{ $icon }}">
                                <i class="{{ $icon }} fs-4 d-block mb-1"></i>
                                <small class="text-muted">{{ substr($icon, 3, 4) }}...</small>
                            </div>
                        @endforeach
                    @else
                        <div class="text-muted p-3">
                            <i class="ri-alert-line me-2"></i>
                            Remix Icons not found. Please check the installation.
                        </div>
                    @endif
                </div>
            </div>

            <!-- Boxicons Tab -->
            <div class="tab-pane fade {{ $selectedIconSet === 'boxicons' ? 'show active' : '' }}"
                 id="{{ $boxiconsContentId }}" role="tabpanel" aria-labelledby="{{ $boxiconsTabId }}">
                <div class="d-flex flex-wrap gap-3 icon-picker"
                     style="max-height: 300px; overflow-y: auto; padding: 8px;" data-icon-set="boxicons">
                    @if(!empty($boxicons))
                        @foreach ($boxicons as $icon)
                            <div class="icon-option border rounded p-2 {{ $selectedSpecificIcon === $icon ? 'border-primary' : '' }}"
                                style="cursor: pointer; min-width: 50px; text-align: center;"
                                data-icon="{{ $icon }}" title="{{ $icon }}">
                                <i class="bx {{ $icon }} fs-4 d-block mb-1"></i>
                                <small class="text-muted">{{ substr($icon, 3, 4) }}...</small>
                            </div>
                        @endforeach
                    @else
                        <div class="text-muted p-3">
                            <i class="bx bx-error me-2"></i>
                            Boxicons not found. Please check the installation.
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <input type="hidden" name="{{ $name }}" id="{{ $inputId }}" value="{{ $selected }}" @if($required) required @endif>
    
    @error($name)
        <div class="text-danger mt-1">{{ $message }}</div>
    @enderror

    <div class="form-text">
        Click an icon to select it. Currently selected: <code id="{{ $displayId }}">{{ $selected }}</code>
    </div>
</div>

@once
    @push('styles')
        <style>
            /* Ensure Remix Icons display correctly */
            [class^="ri-"], [class*=" ri-"] {
                font-family: 'remixicon' !important;
                font-style: normal !important;
                font-weight: normal !important;
                font-variant: normal !important;
                text-transform: none !important;
                line-height: 1 !important;
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
                display: inline-block !important;
            }
            
            /* Ensure Boxicons display correctly */
            [class^="bx"], [class*=" bx"] {
                font-family: 'boxicons' !important;
                font-style: normal !important;
                font-weight: normal !important;
                font-variant: normal !important;
                text-transform: none !important;
                line-height: 1 !important;
                -webkit-font-smoothing: antialiased !important;
                -moz-osx-font-smoothing: grayscale !important;
                display: inline-block !important;
            }
            
            /* Enhanced icon picker styles */
            .icon-picker {
                padding: 8px !important;
            }

            .icon-picker .icon-option {
                transition: all 0.2s ease;
                min-height: 60px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                margin: 2px;
            }
            
            .icon-picker .icon-option:hover {
                background-color: #f8f9fa;
                border-color: rgb(var(--secondary-rgb)) !important;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .icon-picker .icon-option.border-primary {
                background-color: #0d6efd !important;
                border-color: #0d6efd !important;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(13, 110, 253, 0.4);
            }

            .icon-picker .icon-option.border-primary i {
                color: white !important;
            }

            .icon-picker .icon-option.border-primary small {
                color: rgba(255, 255, 255, 0.8) !important;
            }
            
            /* Force icon display */
            .icon-picker .icon-option i {
                font-size: 1.5rem !important;
                color: #333 !important;
            }
            
            /* Unified icon picker container */
            .icon-picker-container {
                background-color: #fff;
                border-color: #dee2e6 !important;
            }

            .icon-picker-header {
                background-color: #f8f9fa !important;
                border-bottom-color: #dee2e6 !important;
                border-top-left-radius: 0.375rem !important;
                border-top-right-radius: 0.375rem !important;
            }

            /* Button group styling */
            .btn-group .btn-outline-secondary {
                border-color: #dee2e6;
                color: #6c757d;
                transition: all 0.2s ease;
                padding: 0.5rem 1rem;
                font-size: 0.875rem;
            }

            .btn-group .btn-outline-secondary:hover {
                background-color: rgba(var(--secondary-rgb), 0.1);
                border-color: rgb(var(--secondary-rgb));
                color: rgb(var(--secondary-rgb));
            }

            .btn-group .btn-check:checked + .btn-outline-secondary {
                background-color: rgb(var(--secondary-rgb));
                border-color: rgb(var(--secondary-rgb));
                color: white !important;
                box-shadow: 0 2px 4px rgba(var(--secondary-rgb), 0.2);
            }

            .btn-group .btn-check:focus + .btn-outline-secondary {
                box-shadow: 0 0 0 0.2rem rgba(var(--secondary-rgb), 0.25);
            }

            /* Natural button sizing with proper padding */
            .btn-group .btn {
                white-space: nowrap;
            }

            /* Remove all border styling from tab content areas
            /* Ensure tab panes have no borders */
            .tab-content .tab-pane {
                border: 0 !important;
                border-radius: 0 !important;
                border-width: 0 !important;
                box-shadow: none !important;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize icon selector for component: {{ $componentId }}
                const componentId = '{{ $componentId }}';
                const inputId = '{{ $inputId }}';
                const displayId = '{{ $displayId }}';
                const tabsId = '{{ $tabsId }}';

                const iconInput = document.getElementById(inputId);
                const selectedIconDisplay = document.getElementById(displayId);
                const componentElement = document.getElementById(componentId);

                if (!componentElement) return; // Component not found

                // Function to handle icon selection within this component
                function handleIconSelection() {
                    const iconOptions = componentElement.querySelectorAll('.icon-option');

                    iconOptions.forEach(option => {
                        // Remove existing listeners to prevent duplicates
                        option.replaceWith(option.cloneNode(true));
                    });

                    // Re-select the options after cloning
                    const refreshedIconOptions = componentElement.querySelectorAll('.icon-option');

                    refreshedIconOptions.forEach(option => {
                        option.addEventListener('click', function() {
                            // Remove active style from all icons in this component
                            componentElement.querySelectorAll('.icon-option').forEach(opt => opt.classList.remove('border-primary'));

                            // Add active style to clicked icon
                            this.classList.add('border-primary');

                            // Set hidden input and display
                            const selectedIcon = this.getAttribute('data-icon');

                            // For Boxicons, we need to add the 'bx' base class
                            let iconClassToStore = selectedIcon;
                            if (selectedIcon.startsWith('bx-') || selectedIcon.startsWith('bxs-') || selectedIcon.startsWith('bxl-')) {
                                iconClassToStore = 'bx ' + selectedIcon;
                            }

                            if (iconInput) {
                                iconInput.value = iconClassToStore;
                            }
                            if (selectedIconDisplay) {
                                selectedIconDisplay.textContent = iconClassToStore;
                            }

                            console.log('Selected icon in ' + componentId + ':', selectedIcon);
                        });
                    });
                }

                // Initialize icon selection handlers
                handleIconSelection();

                // Re-initialize handlers when button group tab is switched
                const tabInputs = componentElement.querySelectorAll('input[data-bs-toggle="tab"]');
                tabInputs.forEach(input => {
                    input.addEventListener('change', function() {
                        if (this.checked) {
                            // Trigger Bootstrap tab change
                            const targetId = this.getAttribute('data-bs-target');
                            const targetPane = document.querySelector(targetId);

                            // Hide all tab panes in this component
                            componentElement.querySelectorAll('.tab-pane').forEach(pane => {
                                pane.classList.remove('show', 'active');
                            });

                            // Show the selected tab pane
                            if (targetPane) {
                                targetPane.classList.add('show', 'active');
                            }

                            // Update aria-selected attributes
                            componentElement.querySelectorAll('input[data-bs-toggle="tab"]').forEach(inp => {
                                inp.setAttribute('aria-selected', 'false');
                            });
                            this.setAttribute('aria-selected', 'true');

                            // Small delay to ensure tab content is fully loaded
                            setTimeout(handleIconSelection, 100);
                        }
                    });
                });
            });
        </script>
    @endpush
@endonce
